#!/usr/bin/env python
"""
Test setup script - primarily sets environment variables for pytest.
Database setup, seeding, and tool registration are handled by conftest.py
when running under pytest.

Usage (standalone, e.g., for manual DB setup):
python ultimate_test_setup.py
"""
import os
import sys
import importlib
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
)
logger = logging.getLogger("test_setup")

def setup_environment():
    """Set up the Django environment variables"""
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.test")
    # Ensure TESTING is set, conftest.py relies on this
    os.environ.setdefault("TESTING", "true")
    logger.info(f"Django settings module: {os.environ.get('DJANGO_SETTINGS_MODULE')}")
    logger.info(f"TESTING environment variable: {os.environ.get('TESTING')}")
    logger.info(f"Current working directory: {os.getcwd()}")

def initialize_django():
    """Initialize Django"""
    try:
        import django
        django.setup()
        logger.info("Django setup successful")
        return True
    except Exception as e:
        logger.error(f"Django setup failed: {e}", exc_info=True)
        return False

def get_database_path():
    """Get the database path from Django settings"""
    # Requires Django to be initialized
    from django.conf import settings
    db_path = settings.DATABASES['default']['NAME']
    logger.info(f"Database path: {db_path}")
    return db_path

def reset_database(db_path):
    """Reset the database - for PostgreSQL, this is a no-op"""
    logger.info("Reset database step skipped for PostgreSQL")

def apply_migrations():
    """Apply Django migrations"""
    # Requires Django to be initialized
    logger.info("Applying Django migrations")
    from django.core.management import call_command
    call_command('migrate', '--noinput')

def setup_database_for_models():
    """Setup the database tables for models without migrations"""
    # Requires Django to be initialized
    logger.info("Creating tables for models without migrations (if needed)")
    from django.db import connection
    from django.apps import apps

    for app_config in apps.get_app_configs():
        logger.debug(f"Processing app: {app_config.label}")
        for model in app_config.get_models():
            table_name = model._meta.db_table
            with connection.cursor() as cursor:
                cursor.execute("SELECT to_regclass(%s)", [table_name])
                table_exists = cursor.fetchone()[0] is not None
            if not table_exists:
                try:
                    logger.info(f"Creating table {table_name}")
                    with connection.schema_editor() as schema_editor:
                        schema_editor.create_model(model)
                except Exception as e:
                    logger.warning(f"Error creating table {table_name}: {e}")

def seed_test_data():
    """Seed the database with minimal test data"""
    # Requires Django to be initialized
    logger.info("Seeding minimal test data")
    from django.core.management import call_command
    # Add seeding commands here if needed for standalone execution
    commands = [
        'seed_db_10_hexacos',
        'seed_db_20_limitations',
        'seed_db_30_domains',
        'seed_db_40_envs',
    ]
    for cmd in commands:
        try:
            call_command(cmd)
            logger.info(f"Successfully seeded {cmd}")
        except Exception as e:
            logger.warning(f"Failed to seed {cmd}: {e}")

def register_tools():
    """Import all tool modules and register tools directly"""
    # Requires Django to be initialized (for sync_tool_registry_with_database)
    logger.info("Registering tools directly")
    tool_modules = [
        'apps.main.agents.tools.tools',
        'apps.main.agents.tools.extra_tools',
        'apps.main.agents.tools.get_user_profile_tool',
        'apps.main.agents.tools.dispatcher_tools',
        'apps.main.agents.tools.mentor_tools',
        'apps.main.agents.tools.update_current_mood_tool',
    ]
    for module_name in tool_modules:
        try:
            logger.debug(f"Importing module {module_name}")
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
            else:
                importlib.import_module(module_name)
        except ImportError as e:
            logger.warning(f"Could not import module {module_name}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error importing module {module_name}: {e}", exc_info=True)

    try:
        logger.info("Synchronizing tool registry with database")
        from apps.main.agents.tools.tools_util import sync_tool_registry_with_database
        sync_tool_registry_with_database()
        logger.info("Successfully synchronized tools with database")
    except Exception as e:
        logger.error(f"Error synchronizing tools: {e}", exc_info=True)

def connect_tools():
    """Connect tools using the management command"""
    # Requires Django to be initialized
    logger.info("Connecting tools")
    try:
        from django.core.management import call_command
        call_command('cmd_tool_connect', reset=True)
        logger.info("Successfully connected tools")
    except Exception as e:
        logger.warning(f"Error connecting tools: {e}")

def ensure_default_llm_configs():
    """Ensure default LLM configurations exist for testing"""
    # Requires Django to be initialized
    logger.info("Ensuring default LLM configurations exist")
    try:
        from apps.main.models import LLMConfig

        # Create a default LLM config
        default_llm_config, _ = LLMConfig.objects.get_or_create(
            name="default-llm-config",
            defaults={
                "model_name": "mistral-small-latest",
                "temperature": 0.7,
                "is_default": True,
                "input_token_price": 0.01,
                "output_token_price": 0.02,
                "is_evaluation": False
            }
        )

        # Create a test LLM config for benchmarks
        test_llm_config, _ = LLMConfig.objects.get_or_create(
            name="test-benchmark-llm",
            defaults={
                "model_name": "test-model",
                "temperature": 0.5,
                "is_default": False,
                "input_token_price": 0.005,
                "output_token_price": 0.01,
                "is_evaluation": False
            }
        )

        logger.info(f"Default LLM configs created/verified: default-llm-config, test-benchmark-llm")
        return default_llm_config
    except Exception as e:
        logger.error(f"Error ensuring default LLM configs exist: {e}", exc_info=True)
        return None

def ensure_default_mentor_agent():
    """Ensure the default mentor agent exists"""
    # Requires Django to be initialized
    logger.info("Ensuring default mentor agent exists")
    try:
        from apps.main.models import GenericAgent

        # Get the default LLM config
        default_llm_config = ensure_default_llm_configs()
        if not default_llm_config:
            logger.error("Failed to create default LLM config, cannot create mentor agent")
            return

        default_schema = {"type": "object", "properties": {}, "additionalProperties": True}
        mentor_agent, created = GenericAgent.objects.get_or_create(
            role="mentor",
            defaults={
                "description": "Default mentor agent for testing",
                "system_instructions": "Provide empathetic and helpful responses.",
                "input_schema": default_schema, "output_schema": default_schema,
                "state_schema": {}, "memory_schema": {},
                "llm_config": default_llm_config, "version": "1.0.0", "is_active": True,
                "langgraph_node_class": "apps.main.agents.mentor_agent.MentorAgent",
                "processing_timeout": 30
            }
        )
        if created: logger.info("Created default GenericAgent for role 'mentor'.")

        # Create other agent roles for testing
        for role in ["orchestrator", "strategy", "resource", "engagement", "psychological", "ethical", "wheel_activity"]:
            agent, created = GenericAgent.objects.get_or_create(
                role=role,
                defaults={
                    "description": f"Default {role} agent for testing",
                    "system_instructions": f"Provide {role} functionality.",
                    "input_schema": default_schema, "output_schema": default_schema,
                    "state_schema": {}, "memory_schema": {},
                    "llm_config": default_llm_config, "version": "1.0.0", "is_active": True,
                    "langgraph_node_class": f"apps.main.agents.{role}_agent.{role.title().replace('_', '')}Agent",
                    "processing_timeout": 30
                }
            )
            if created: logger.info(f"Created default GenericAgent for role '{role}'.")
    except Exception as e:
        logger.error(f"Error ensuring mentor GenericAgent exists: {e}", exc_info=True)


def run_standalone_setup():
    """Run the setup process for standalone execution."""
    logger.info("--- Running Standalone Setup ---")
    setup_environment()
    if not initialize_django(): return False
    db_path = get_database_path()
    reset_database(db_path)
    apply_migrations()
    setup_database_for_models() # Create tables for non-migrated models
    if os.environ.get('SEED_TEST_DATA', 'true').lower() != 'false':
        seed_test_data()
        # Ensure default LLM configs exist first
        ensure_default_llm_configs()
        register_tools()
        connect_tools()
        ensure_default_mentor_agent()
    # Set environment variable to indicate database has been initialized by this script
    os.environ["TEST_DB_INITIALIZED_BY_SCRIPT"] = "true"
    logger.info("--- Standalone Setup Complete ---")
    return True

def run_pytest_setup():
    """Setup steps relevant when running under pytest."""
    logger.info("--- Running Pytest Setup (Environment Variables Only) ---")
    setup_environment()
    # DO NOT initialize Django here - let pytest-django handle it.
    # DO NOT seed/register tools here - let conftest.py handle it.
    # Set environment variable to indicate database setup should be done by conftest
    os.environ["TEST_DB_INITIALIZED_BY_SCRIPT"] = "false"
    logger.info("--- Pytest Setup Complete ---")
    return True

if __name__ == "__main__":
    logger.info("Running ultimate_test_setup.py as main script.")
    # Determine if running under pytest (simple check, might need refinement)
    is_pytest_run = "pytest" in sys.modules or os.environ.get("PYTEST_CURRENT_TEST")
    testing_env_var = os.environ.get("TESTING", "false").lower()

    logger.info(f"is_pytest_run: {is_pytest_run}")
    logger.info(f"TESTING env var: {testing_env_var}")

    # If TESTING is explicitly true, always run pytest setup.
    # Otherwise, check if running under pytest. If not, run standalone setup.
    if testing_env_var == "true":
        logger.info("TESTING env var is true, running pytest setup.")
        success = run_pytest_setup()
    elif is_pytest_run:
        logger.info("Running under pytest, running pytest setup.")
        success = run_pytest_setup()
    else:
        logger.info("Not running under pytest and TESTING is not true, running standalone setup.")
        success = run_standalone_setup()

    sys.exit(0 if success else 1)
