"""
Extractors for agent and tool definitions used in testing environments.
"""
import json
import logging
import os
import importlib.util
import re
import pkgutil
import importlib
from unittest.mock import MagicMock
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

# Attempt to import the tool registration info getter
try:
    from apps.main.agents.tools.tools_util import get_tool_registry_info
except ImportError:
    logger.error("Failed to import get_tool_registry_info. Dynamic tool discovery will fail.")
    # Create a mock function that returns an empty dictionary
    def get_tool_registry_info():
        """Mock function that returns an empty dictionary."""
        logger.warning("Using mock get_tool_registry_info function.")
        return {
            'analyze_psychological_state': {
                'name': 'Analyze Psychological State',
                'description': 'Analyzes the user\'s current psychological state based on context.',
                'path': 'apps.main.agents.tools.psychological_tools.analyze_psychological_state',
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'user_profile_id': {'type': 'string'},
                        'context': {'type': 'object'}
                    },
                    'required': ['user_profile_id']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'mood': {'type': 'string'},
                        'energy_level': {'type': 'string'},
                        'stress_level': {'type': 'string'},
                        'cognitive_load': {'type': 'string'},
                        'emotional_balance': {'type': 'string'},
                        'confidence': {'type': 'number'}
                    }
                }
            },
            'get_trust_metrics': {
                'name': 'Get Trust Metrics',
                'description': 'Retrieves trust metrics for a user.',
                'path': 'apps.main.agents.tools.psychological_tools.get_trust_metrics',
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'user_profile_id': {'type': 'string'}
                    },
                    'required': ['user_profile_id']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'trust_level': {'type': 'number'},
                        'engagement_trust': {'type': 'number'},
                        'action_trust': {'type': 'number'},
                        'disclosure_trust': {'type': 'number'},
                        'phase_duration_days': {'type': 'number'},
                        'confidence': {'type': 'number'}
                    }
                }
            }
        }

logger = logging.getLogger(__name__)

class AgentDefinitionsExtractor:
    """
    Extracts agent definitions and tool mappings for use in testing environments without database access.
    """

    def __init__(self, cache_file_path=None, tool_mapping_cache_path=None):
        """
        Initialize the extractor with optional caching.

        Args:
            cache_file_path: Optional path to cache extracted definitions
            tool_mapping_cache_path: Optional path to cache extracted tool mappings
        """
        # Agent definitions paths
        self.cache_file_path = cache_file_path or os.path.join(
            os.path.dirname(__file__),
            '..', 'tests', 'test_data',
            'agent_definitions_cache.json'
        )
        self.seed_file_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
            'apps', 'main', 'management', 'commands', 'seed_db_80_agents.py'
        )

        # Tool mapping paths
        self.tool_mapping_cache_path = tool_mapping_cache_path or os.path.join(
            os.path.dirname(__file__),
            '..', 'tests', 'test_data',
            'tool_mapping_cache.json'
        )
        self.tool_connect_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
            'apps', 'main', 'management', 'commands', 'cmd_tool_connect.py'
        )

        # Storage for extracted data
        self.agent_definitions = {}
        self.agent_tool_mappings = {}
        self.common_tools = []

    def extract_definitions(self, use_cache=True):
        """
        Extract agent definitions from the seed command file.

        Args:
            use_cache: Whether to use cached definitions if available

        Returns:
            dict: Extracted agent definitions by role
        """

        # Reset instance variables if not using cache
        if not use_cache:
            self.agent_definitions = {}
        # Check cache first if enabled
        if use_cache and os.path.exists(self.cache_file_path):
            try:
                with open(self.cache_file_path, 'r') as f:
                    cache_data = json.load(f)
                    self.agent_definitions = cache_data.get('agent_definitions', {})
                    # Note: agent_tools was likely a typo in the original, not used elsewhere

                    if self.agent_definitions:
                        return self.agent_definitions
            except Exception as e:
                logger.warning(f"Cache reading error: {str(e)}, will extract fresh definitions")

        # Direct file reading fallback if cache not available or valid
        if not os.path.exists(self.seed_file_path):
            logger.warning(f"Cannot find seed file at: {self.seed_file_path}")
            return self._extract_hardcoded_definitions()

        try:
            # Set up mock environment for Django models
            self._setup_django_mocks()

            # Load command module directly using spec
            spec = importlib.util.spec_from_file_location("seed_db_80_agents", self.seed_file_path)
            seed_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(seed_module)

            # Find Command class in the module
            command_class = None
            for name, obj in seed_module.__dict__.items():
                if name == "Command" and callable(getattr(obj, "handle", None)):
                    command_class = obj
                    break

            if not command_class:
                logger.warning("Could not find Command class in seed file")
                return self._extract_hardcoded_definitions()

            # Setup capture for agent definitions
            def mock_agent_get_or_create(role, defaults=None):
                """Capture agent definition during mocked get_or_create"""
                if defaults:
                    # Create a clean copy without any MagicMock objects
                    clean_defaults = {}
                    for key, value in defaults.items():
                        # Skip ManyToManyField relationships which require special handling
                        if key == 'available_tools':
                            continue
                        # Store primitive values directly
                        if isinstance(value, (str, int, float, bool, list, dict, type(None))):
                            clean_defaults[key] = value
                        else:
                            # Convert other types to strings for serialization
                            clean_defaults[key] = str(value)

                    # Store the definition by role
                    self.agent_definitions[role] = {
                        'role': role,
                        **clean_defaults
                    }
                return MagicMock(), True

            # Apply patches
            import unittest.mock
            patches = [
                unittest.mock.patch('apps.main.models.GenericAgent.objects.get_or_create',
                      side_effect=mock_agent_get_or_create),
                unittest.mock.patch('apps.main.models.AgentTool.objects.get_or_create',
                      return_value=(MagicMock(), True)),
                unittest.mock.patch('apps.main.models.BenchmarkMetric.objects.get_or_create',
                      return_value=(MagicMock(), True)),
                unittest.mock.patch('django.db.transaction.atomic',
                      return_value=MagicMock().__enter__.return_value),
            ]

            # Start all patches
            for p in patches:
                p.start()

            # Create command instance with overridden stdout
            command = command_class()
            command.stdout = MagicMock()

            # Extract agent definitions by calling methods directly
            agent_methods = [
                'create_mentor_agent',
                'create_orchestrator_agent',
                'create_resource_agent',
                'create_engagement_agent',
                'create_psychological_agent',
                'create_strategy_agent',
                'create_wheel_activity_agent',
                'create_ethical_agent',
                'create_dispatcher_agent'
            ]

            for method_name in agent_methods:
                if hasattr(command, method_name) and callable(getattr(command, method_name)):
                    try:
                        getattr(command, method_name)()
                    except Exception as e:
                        logger.warning(f"Error calling {method_name}: {str(e)}")

            # Stop all patches
            for p in patches:
                p.stop()

            # Cache the extracted definitions if a path is provided
            if self.cache_file_path and self.agent_definitions:
                # Convert any MagicMock objects to serializable form
                serializable_definitions = {}
                try :
                    for role, definition in self.agent_definitions.items():
                        serializable_def = {}
                        # Ensure role is a string for JSON key
                        role_key = role._mock_name if hasattr(role, '_mock_name') else str(role)

                        for key, value in definition.items():
                            # Handle non-serializable types
                            if key == "role" and isinstance(value, MagicMock):
                                serializable_def[key] = str(value._mock_name)
                            elif key == 'available_tools':
                                # Skip ManyToManyField reference that won't serialize
                                continue
                            else:
                                serializable_def[key] = value
                        serializable_definitions[role_key] = serializable_def

                    # Create directory if it doesn't exist
                    os.makedirs(os.path.dirname(self.cache_file_path), exist_ok=True)

                    # Write to cache file
                    with open(self.cache_file_path, 'w') as f:
                        json.dump({
                            'agent_definitions': serializable_definitions,
                            'agent_tools': {}  # Empty for now, add tool extraction later if needed
                        }, f, indent=2)
                except Exception as e:
                    logger.warning(f"Error caching definitions: {str(e)}")


            return self.agent_definitions

        except Exception as e:
            logger.warning(f"Error extracting definitions: {str(e)}")
            return self._extract_hardcoded_definitions()

    def extract_tool_mappings(self, use_cache=True):
        """
        Extract tool mappings from the cmd_tool_connect.py file.

        Args:
            use_cache: Whether to use cached mappings if available

        Returns:
            tuple: (dict with agent tool mappings, list of common tools)
        """
        # Reset instance variables if not using cache
        if not use_cache:
            self.agent_tool_mappings = {}
            self.common_tools = []

        # Check cache first if enabled
        if use_cache and os.path.exists(self.tool_mapping_cache_path):
            try:
                with open(self.tool_mapping_cache_path, 'r') as f:
                    cache_data = json.load(f)
                    self.agent_tool_mappings = cache_data.get('agent_tool_mappings', {})
                    self.common_tools = cache_data.get('common_tools', [])

                    if self.agent_tool_mappings:
                        return self.agent_tool_mappings, self.common_tools
            except Exception as e:
                logger.warning(f"Tool mapping cache reading error: {str(e)}, will extract fresh mappings")

        # Direct file reading if cache not available or valid
        if not os.path.exists(self.tool_connect_path):
            logger.warning(f"Cannot find tool connect file at: {self.tool_connect_path}")
            return self._extract_hardcoded_tool_mappings()

        try:
            # Set up mock environment for Django models
            self._setup_django_mocks()

            # Read the file as text
            with open(self.tool_connect_path, 'r') as f:
                file_content = f.read()

            # Extract AGENT_TOOL_MAPPING using regex
            mapping_match = re.search(r'AGENT_TOOL_MAPPING\s*=\s*\{([^}]*)\}', file_content, re.DOTALL)
            if mapping_match:
                mapping_text = mapping_match.group(1)

                # Extract role-to-tools mappings
                role_pattern = r"AgentRole\.([A-Z_]+):\s*\[(.*?)\],"
                role_matches = re.finditer(role_pattern, mapping_text, re.DOTALL)

                for role_match in role_matches:
                    role = role_match.group(1).lower()  # Convert to lowercase for consistency
                    tools_text = role_match.group(2)

                    # Extract tool names
                    tool_pattern = r"'([^']*)',"
                    tools = re.findall(tool_pattern, tools_text)
                    self.agent_tool_mappings[role] = tools

            # Extract COMMON_TOOLS using regex
            common_match = re.search(r'COMMON_TOOLS\s*=\s*\[(.*?)\]', file_content, re.DOTALL)
            if common_match:
                common_text = common_match.group(1)

                # Extract tool names
                tool_pattern = r"'([^']*)'"
                self.common_tools = re.findall(tool_pattern, common_text)

            # Save to cache if specified
            if self.tool_mapping_cache_path:
                try:
                    # Create directory if it doesn't exist
                    os.makedirs(os.path.dirname(self.tool_mapping_cache_path), exist_ok=True)

                    # Write to cache file
                    with open(self.tool_mapping_cache_path, 'w') as f:
                        json.dump({
                            'agent_tool_mappings': self.agent_tool_mappings,
                            'common_tools': self.common_tools
                        }, f, indent=2)
                except Exception as e:
                    logger.warning(f"Error caching tool mappings: {str(e)}")

            return self.agent_tool_mappings, self.common_tools

        except Exception as e:
            logger.warning(f"Error extracting tool mappings: {str(e)}")
            return self._extract_hardcoded_tool_mappings()

    def _extract_hardcoded_tool_mappings(self):
        """Fallback to provide hardcoded tool mappings"""
        logger.warning("Using hardcoded fallback tool mappings")

        fallback_mappings = {
            'mentor': [
                'get_user_profile',
                'get_activity_details',
                'record_user_feedback'
            ],
            'orchestrator': [
                'get_user_profile',
                'get_recent_interactions'
            ],
            'strategy': [
                'get_user_profile',
                'get_activity_details'
            ]
            # Add other roles as needed
        }

        fallback_common_tools = [] # Define common tools if any

        self.agent_tool_mappings = fallback_mappings
        self.common_tools = fallback_common_tools
        return fallback_mappings, fallback_common_tools

    def _setup_django_mocks(self):
        """Set up mocks for Django modules to enable importing the command"""
        # Mock essential Django modules
        import sys
        sys.modules['django.db'] = MagicMock()
        sys.modules['django.db.models'] = MagicMock()
        sys.modules['django.db.transaction'] = MagicMock()
        sys.modules['django.contrib.contenttypes.models'] = MagicMock()
        sys.modules['django.utils'] = MagicMock()
        sys.modules['django.utils.timezone'] = MagicMock()
        sys.modules['django.core'] = MagicMock()
        sys.modules['django.core.management'] = MagicMock()
        sys.modules['django.core.management.base'] = MagicMock()
        sys.modules['apps.main.models'] = MagicMock() # Mock the specific models module

        # Create BaseCommand mock
        base_command = type('BaseCommand', (), {
            '__init__': lambda self: None,
            'handle': lambda self, *args, **options: None,
            # Add mock attributes and methods commonly accessed on BaseCommand
            'create_parser': lambda self, prog_name, subcommand: MagicMock(),
            'base_stealth_options': (), # Add missing attribute
            'stealth_options': (), # Add related attribute
            'requires_system_checks': True, # Common requirement for commands
            'requires_migrations_checks': True, # Common requirement for commands
            'requires_apps_ready': True, # Common requirement for commands
            # Add other potentially accessed attributes/methods as needed
            # 'output_transaction': False,
            # 'leave_locale_alone': False,
            # 'suppress_output': [],
            # 'check': lambda self, app_configs, **kwargs: [], # Mock check method
        })
        sys.modules['django.core.management.base'].BaseCommand = base_command

        # Mock AgentRole enum if needed by the seed/connect scripts
        agent_role_enum = type('AgentRole', (), {
            'MENTOR': 'mentor',
            'ORCHESTRATOR': 'orchestrator',
            'RESOURCE': 'resource',
            'ENGAGEMENT': 'engagement',
            'PSYCHOLOGICAL': 'psychological',
            'STRATEGY': 'strategy',
            'WHEEL_ACTIVITY': 'wheel_activity',
            'ETHICAL': 'ethical',
            'DISPATCHER': 'dispatcher',
            # Add other roles as needed
            '__getattr__': lambda self, name: name.lower() # Fallback for other roles
        })
        sys.modules['apps.main.models'].AgentRole = agent_role_enum()


    def _extract_hardcoded_definitions(self):
        """Fallback to provide hardcoded minimal agent definitions"""
        logger.warning("Using hardcoded fallback definitions")

        # Minimum viable agent definitions
        fallback_definitions = {
            'mentor': {
                'role': 'mentor',
                'system_instructions': "You are the Mentor Agent, the primary interface between the Game of Life system and the user.",
                'recommend_models': [],
                'read_models': [],
                'write_models': []
            },
            'orchestrator': {
                'role': 'orchestrator',
                'system_instructions': "You are the Orchestrator Agent, responsible for coordinating the multi-agent workflow.",
                'recommend_models': [],
                'read_models': [],
                'write_models': []
            },
            'strategy': {
                'role': 'strategy',
                'system_instructions': "You are the Strategy Agent, responsible for formulating strategies for activity selection.",
                'recommend_models': [],
                'read_models': [],
                'write_models': []
            },
            'dispatcher': {
                'role': 'dispatcher',
                'system_instructions': "You are the Dispatcher Agent, responsible for analyzing user input and routing to appropriate workflows.",
                'recommend_models': [],
                'read_models': [],
                'write_models': []
            }
            # Add other essential agents if needed
        }

        self.agent_definitions = fallback_definitions
        return fallback_definitions

    def get_agent_definition(self, role, use_cache=True):
        """
        Get a specific agent definition by role.

        Args:
            role: The agent role to retrieve
            use_cache: Whether to use cached definitions if available

        Returns:
            dict: The agent definition or None if not found
        """
        if not self.agent_definitions or not use_cache:
            self.extract_definitions(use_cache)

        # Normalize role to lowercase for lookup
        role_lower = str(role).lower()
        return self.agent_definitions.get(role_lower)


class ToolDefinitionsExtractor:
    """
    Provides tool definitions and standardized mock responses for testing by dynamically
    discovering tools registered within the application.
    """
    # Packages where tool functions are expected to be defined and registered.
    TOOL_PACKAGES = [
        "apps.main.agents.tools", # Assuming tools might be directly here or in submodules
        # Add other top-level tool packages if necessary
    ]

    def __init__(self):
        """
        Initialize the extractor and discover tools.
        """
        self.tool_definitions = {}
        self.tool_mock_responses = {}
        # Discover tools immediately on initialization
        self._discover_tools_from_code()

    def _import_all_modules_from_package(self, package_name: str):
        """
        Recursively imports all modules in the given package to trigger tool registration.
        """
        try:
            package = importlib.import_module(package_name)
            if hasattr(package, '__path__'):
                for finder, name, ispkg in pkgutil.walk_packages(package.__path__, package.__name__ + "."):
                    try:
                        importlib.import_module(name)
                    except Exception as e:
                        logger.warning(f"Failed to import submodule {name}: {e}")
            else:
                # The package is a single module
                 importlib.import_module(package_name)
        except ImportError as e:
             logger.warning(f"Could not import package {package_name}: {e}")
        except Exception as e:
            logger.error(f"Error during dynamic import of {package_name}: {e}")


    def _discover_tools_from_code(self):
        """
        Discovers tools by importing relevant packages and querying the tool registry.
        Handles AppRegistryNotReady errors gracefully during test setup.
        """
        logger.debug(f"Attempting to discover tools from packages: {self.TOOL_PACKAGES}")
        try:
            # Check if we can even import Django core exceptions first
            try:
                from django.core.exceptions import AppRegistryNotReady
            except ImportError:
                # If Django isn't installed or setup at all, we can't check readiness
                AppRegistryNotReady = type('AppRegistryNotReady', (Exception,), {}) # Dummy exception

            # Check if apps are ready (if possible)
            apps_ready = False
            try:
                from django.apps import apps
                apps_ready = apps.apps_ready
            except (ImportError, AppRegistryNotReady):
                logger.warning("Could not check Django app readiness, assuming not ready for tool discovery.")
                apps_ready = False # Assume not ready if check fails

            if not apps_ready:
                logger.warning("Django apps not ready. Skipping dynamic tool discovery in ToolDefinitionsExtractor.")
                # Optionally load minimal hardcoded tools if needed for basic tests
                # self._load_minimal_hardcoded_tools()
                return # Exit early

            # --- Proceed with discovery only if apps seem ready ---

            if not get_tool_registry_info:
                logger.error("get_tool_registry_info is not available. Cannot discover tools.")
                return

            # Import all modules within the specified packages to ensure decorators run
            for package_name in self.TOOL_PACKAGES:
                self._import_all_modules_from_package(package_name)

            # Get the tool info from the central registry
            registered_tools = get_tool_registry_info()
            if not registered_tools:
                 logger.warning("Tool registry is empty after importing packages.")
                 return

            self.tool_definitions = registered_tools
            logger.info(f"Discovered {len(self.tool_definitions)} tools dynamically.")

            # Generate mock responses based on discovered output schemas
            for tool_code, definition in self.tool_definitions.items():
                self.tool_mock_responses[tool_code] = self._generate_mock_response(
                    tool_code, definition.get('output_schema', {})
                )

        except (ImportError, AppRegistryNotReady, AttributeError) as e:
             # Catch errors likely related to Django not being ready or mocks failing
             logger.warning(f"Failed dynamic tool discovery due to potential Django init issue: {type(e).__name__} - {e}. Proceeding without dynamically discovered tools.")
             # Ensure definitions are empty if discovery fails
             self.tool_definitions = {}
             self.tool_mock_responses = {}
             # Optionally load minimal hardcoded tools if needed
             # self._load_minimal_hardcoded_tools()
        except Exception as e:
            # Catch any other unexpected errors during discovery, including SynchronousOnlyOperation
            if "SynchronousOnlyOperation" in str(e):
                logger.warning(f"Tool discovery failed due to async context: {e}. Proceeding without dynamically discovered tools.")
            else:
                logger.error(f"Unexpected error during tool discovery: {e}", exc_info=True)
            self.tool_definitions = {}
            self.tool_mock_responses = {}


    def extract_definitions(self):
        """
        Retrieve the discovered tool definitions.
        Definitions are loaded during __init__.

        Returns:
            dict: Discovered tool definitions by code
        """
        # Definitions are loaded in __init__ via _discover_tools_from_code
        if not self.tool_definitions:
             logger.warning("Tool definitions not loaded, attempting discovery again.")
             self._discover_tools_from_code() # Attempt discovery if somehow empty
        return self.tool_definitions


    def _generate_mock_response(self, tool_code, output_schema):
        """Generate a mock response based on the output schema"""
        # Start with a template based on common patterns
        if tool_code == 'get_user_profile':
            return {
                "id": "mock-user-id",
                "profile_name": "Test User",
                "demographics": {"age": 30, "occupation": "Software Developer"},
                "traits": {
                    "openness": {"strength": 75},
                    "conscientiousness": {"strength": 65},
                    "extraversion": {"strength": 45}
                },
                "goals": [
                    {"id": "goal-1", "title": "Improve creative writing skills", "importance_according_user": 85}
                ],
                "trust_level": {"value": 60, "phase": "Foundation"}
            }
        elif tool_code == 'get_activity_details':
            return {
                "id": "mock-activity-id",
                "name": "Creative Writing Exercise",
                "description": "A short creative writing session to develop your skills",
                "instructions": "Find a quiet space and write for 20 minutes about a memory from your childhood.",
                "challenge_rating": 60,
                "challenge_breakdown": {"openness": 70, "conscientiousness": 55},
                "duration_range": {"min": 15, "max": 30},
                "domains": [{"code": "creative", "strength": 80}],
                "value_proposition": "Enhances creativity and self-expression"
            }
        elif tool_code == 'record_user_feedback':
             return {"status": "success", "feedback_id": "mock-feedback-123"}
        elif tool_code == 'get_recent_interactions':
             return {"interactions": [{"type": "chat", "timestamp": "2024-01-15T10:00:00Z", "summary": "User asked about activities"}]}


        # For other tools, generate responses by looking at the output schema
        return self._create_schema_based_mock(output_schema)

    def _create_schema_based_mock(self, schema):
        """Create a mock response based on a JSON schema"""
        if not isinstance(schema, dict):
            return "Mock response"

        # Handle different schema types
        if 'type' in schema:
            schema_type = schema['type']

            if schema_type == 'object':
                result = {}
                # Process properties
                for prop_name, prop_schema in schema.get('properties', {}).items():
                    result[prop_name] = self._create_schema_based_mock(prop_schema)
                return result

            elif schema_type == 'array':
                # Create a single item array
                items_schema = schema.get('items', {})
                return [self._create_schema_based_mock(items_schema)]

            elif schema_type == 'string':
                # Provide more specific mock strings based on description or name
                description = schema.get('description', '').lower()
                if 'id' in description:
                    return "mock-id-123"
                if 'name' in description:
                    return "Mock Name"
                if 'status' in description:
                    return "success"
                if 'timestamp' in description:
                    return "2024-01-01T12:00:00Z"
                return f"Mock {schema.get('description', 'string value')}"

            elif schema_type in ['number', 'integer']:
                description = schema.get('description', '').lower()
                if 'percentage' in description:
                    return 75
                if 'rating' in description:
                    return 8
                if 'count' in description:
                    return 3
                return 42

            elif schema_type == 'boolean':
                return True

        # Default fallback
        return "Mock response"

    # Removed _extract_hardcoded_definitions as it's replaced by dynamic discovery.
    # Add a minimal fallback here ONLY if dynamic discovery fails catastrophically
    # and some basic tools are absolutely required for tests to even run.
    # def _load_minimal_hardcoded_tools(self):
    #     logger.error("CRITICAL: Dynamic tool discovery failed. Falling back to minimal hardcoded tools.")
    #     self.tool_definitions = { ... minimal tools ... }
    #     # Generate mocks for these minimal tools


    def get_tool_definition(self, tool_code):
        """Get a specific tool definition by code"""
        # Ensure definitions are loaded (should be done in __init__)
        if not self.tool_definitions:
            self.extract_definitions() # Attempt discovery if needed

        return self.tool_definitions.get(tool_code)

    def get_mock_response(self, tool_code, input_data=None):
        """Get a mock response for a specific tool"""
         # Ensure definitions/mocks are loaded (should be done in __init__)
        if not self.tool_mock_responses:
             self.extract_definitions() # Attempt discovery if needed

        return self.tool_mock_responses.get(tool_code, {"result": f"Mock result for {tool_code}"})
