import pytest
from apps.main.models import BenchmarkScenario, BenchmarkTag, EvaluationCriteriaTemplate

@pytest.mark.django_db
@pytest.mark.skip(reason="Temporarily skipping due to isinstance() error - investigating")
def test_benchmark_scenario_variations():
    """
    Test the ManyToMany relationship for scenario variations.
    """
    # Import the utility function
    from apps.main.tests.utils import create_test_scenario

    # Create a base scenario
    base_scenario = create_test_scenario(
        name="Base Scenario",
        description="A base scenario for testing.",
        agent_role="test_agent",
        input_data={"input": "base"},
        metadata={"expected": "base_output"},
        is_active=True,
        version=1
    )
    # Set is_latest flag
    BenchmarkScenario.objects.filter(pk=base_scenario.pk).update(is_latest=True)
    base_scenario.refresh_from_db()

    # Create a variation scenario
    variation_scenario = create_test_scenario(
        name="Variation Scenario",
        description="A variation of the base scenario.",
        agent_role="test_agent",
        input_data={"input": "variation"},
        metadata={"expected": "variation_output"},
        is_active=True,
        version=1
    )
    # Set is_latest flag
    BenchmarkScenario.objects.filter(pk=variation_scenario.pk).update(is_latest=True)
    variation_scenario.refresh_from_db()

    # Link the variation to the base scenario
    variation_scenario.variations.add(base_scenario)

    # Assertions
    assert base_scenario in variation_scenario.variations.all()
    assert variation_scenario in base_scenario.base_scenario_set.all() # Check reverse relationship

    # Test removing the relationship
    variation_scenario.variations.remove(base_scenario)
    assert base_scenario not in variation_scenario.variations.all()
    assert variation_scenario not in base_scenario.base_scenario_set.all()

@pytest.mark.django_db
@pytest.mark.skip(reason="Temporarily skipping due to isinstance() error - investigating")
def test_benchmark_scenario_tags():
    """Test adding tags to a scenario."""
    tag1 = BenchmarkTag.objects.create(name="Tag1", description="First Tag")
    tag2 = BenchmarkTag.objects.create(name="Tag2", description="Second Tag")

    # Import the utility function
    from apps.main.tests.utils import create_test_scenario

    # Create a scenario with tags
    scenario = create_test_scenario(
        name="Tagged Scenario",
        description="Scenario with tags.",
        agent_role="tag_agent",
        input_data={},
        metadata={},
        version=1
    )
    # Set is_latest flag
    BenchmarkScenario.objects.filter(pk=scenario.pk).update(is_latest=True)
    scenario.refresh_from_db()

    scenario.tags.add(tag1, tag2)

    assert scenario.tags.count() == 2
    assert tag1 in scenario.tags.all()
    assert tag2 in scenario.tags.all()

    # Check reverse relationship
    assert scenario in tag1.scenarios.all()
    assert scenario in tag2.scenarios.all()


@pytest.mark.django_db
def test_evaluation_criteria_template_creation():
    """Test creating an EvaluationCriteriaTemplate instance."""
    template_name = "Test Template"
    template_desc = "A template for testing."
    template_criteria = {
        "dimension1": ["Criterion 1a", "Criterion 1b"],
        "dimension2": ["Criterion 2a"]
    }

    template = EvaluationCriteriaTemplate.objects.create(
        name=template_name,
        description=template_desc,
        criteria=template_criteria
    )

    assert template.pk is not None
    assert template.name == template_name
    assert template.description == template_desc
    assert template.criteria == template_criteria
    assert str(template) == template_name

    # Test uniqueness constraint
    with pytest.raises(Exception): # Use generic Exception or IntegrityError if specific
        EvaluationCriteriaTemplate.objects.create(
            name=template_name, # Same name
            description="Another description",
            criteria={}
        )
