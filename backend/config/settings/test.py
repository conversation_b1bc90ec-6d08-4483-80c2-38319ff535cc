from .base import *
import logging
import os
import environ

logger = logging.getLogger(__name__)

# Build paths inside the project like this: BASE_DIR / 'subdir'.
# Note: We're setting this to /usr/src/app/ for Docker
# BASE_DIR is already defined in base.py, but we need it here for the .env.test path
# This ensures we are loading the .env.test from the backend directory
BACKEND_ROOT = Path(__file__).resolve().parent.parent.parent # Go to the 'backend' directory

# Load environment variables from backend/.env.test
# This should be done before any other environment variable access
environ.Env.read_env(os.path.join(BACKEND_ROOT, '.env.test'))

# Initialize django-environ for test-specific settings
env = environ.Env(
    # Set defaults for test environment
    DEBUG=(bool, False),
    DATABASE_URL=(str, "postgres://test_user:test_password@localhost:5432/test_goali")
)

# Test-specific settings
DEBUG = env('DEBUG', default=False) # Read DEBUG from .env.test, fallback to False
ALLOWED_HOSTS = env.list('ALLOWED_HOSTS', default=['localhost', '127.0.0.1']) # Read ALLOWED_HOSTS from .env.test

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'channels',
    'apps.main',
    'apps.user',
    'apps.activity',
]

# --- Use PostgreSQL for all tests ---
# Ensures consistency between local and CI environments.
logger.info("Using PostgreSQL database for tests")

# Smart environment detection for database configuration
def get_database_config():
    """
    Determine the appropriate database configuration based on environment.

    Returns:
        dict: Database configuration for Django DATABASES setting
    """
    # Check if we're running in Docker (test-db host should be resolvable)
    import socket

    try:
        # Try to resolve test-db hostname (Docker environment)
        socket.gethostbyname('test-db')
        is_docker = True
        logger.info("Detected Docker environment (test-db host resolvable)")
    except socket.gaierror:
        # test-db not resolvable, assume local environment
        is_docker = False
        logger.info("Detected local environment (test-db host not resolvable)")

    if is_docker:
        # Use Docker database configuration
        database_url = "***********************************************/test_goali"
        logger.info(f"Using Docker database configuration: {database_url}")
    else:
        # Use local database configuration
        database_url = "postgres://test_user:test_password@localhost:5432/test_goali"
        logger.info(f"Using local database configuration: {database_url}")

    # Parse the database URL using django-environ
    temp_env = environ.Env()
    # Temporarily set the DATABASE_URL for parsing
    os.environ['TEMP_DATABASE_URL'] = database_url
    try:
        return temp_env.db('TEMP_DATABASE_URL')
    finally:
        # Clean up the temporary environment variable
        if 'TEMP_DATABASE_URL' in os.environ:
            del os.environ['TEMP_DATABASE_URL']

# Set database configuration
try:
    DATABASES = {
        'default': get_database_config()
    }
except Exception as e:
    logger.warning(f"Error in smart database detection: {e}")
    # Fallback to environment variable or default
    if 'DATABASE_URL' in os.environ:
        logger.info(f"Falling back to DATABASE_URL from environment: {os.environ['DATABASE_URL']}")
        DATABASES = {
            'default': env.db(),  # Uses DATABASE_URL environment variable
        }
    else:
        # Final fallback
        logger.info("Using final fallback PostgreSQL database configuration")
        DATABASES = {
            'default': {
                'ENGINE': 'django.db.backends.postgresql',
                'NAME': 'test_goali',
                'USER': 'test_user',
                'PASSWORD': 'test_password',
                'HOST': 'localhost',
                'PORT': '5432',
            }
        }

# Set a default Time Zone for tests (important for timezone-aware datetime operations)
TIME_ZONE = 'UTC'
USE_TZ = True # Ensure timezone support is enabled

# Use in-memory channel layer for tests
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer"
    },
}

# Simplify middleware for tests
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware', # Added for admin app compatibility
]

# Disable routers for tests
DATABASE_ROUTERS = []

# Celery configuration for tests
# Default to localhost for CI environment where Redis runs as a service on the runner host.
# Local Docker tests override this via .env.test (redis://redis:6379/0)
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = CELERY_BROKER_URL # Keep result backend consistent
CELERY_TASK_ALWAYS_EAGER = True # Optional: Run tasks synchronously for simpler testing
CELERY_TASK_EAGER_PROPAGATES = True # Optional: Propagate exceptions from eager tasks
